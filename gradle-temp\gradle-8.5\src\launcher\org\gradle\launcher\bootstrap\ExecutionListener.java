/*
 * Copyright 2010 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.gradle.launcher.bootstrap;

/**
 * Allows an execution action to provide status information to the execution context.
 *
 * <p>Note: if the action does not call {@link #onFailure(Throwable)}, then the execution is assumed to have
 * succeeded.</p>
 */
public interface ExecutionListener {
    /**
     * Reports a failure of the execution. Note that it is the caller's responsibility to perform any logging of the
     * failure.
     *
     * @param failure The execution failure. This exception has already been logged.
     */
    void onFailure(Throwable failure);
}
