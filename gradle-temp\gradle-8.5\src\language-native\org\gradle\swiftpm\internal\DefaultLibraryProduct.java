/*
 * Copyright 2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.swiftpm.internal;

import org.gradle.nativeplatform.Linkage;

public class DefaultLibraryProduct extends AbstractProduct {
    private final Linkage linkage;

    public DefaultLibraryProduct(String name, DefaultTarget target, Linkage linkage) {
        super(name, target);
        this.linkage = linkage;
    }

    public Linkage getLinkage() {
        return linkage;
    }

    @Override
    public boolean isExecutable() {
        return false;
    }
}
