/*
 * Copyright 2021 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.configurationcache

import org.gradle.internal.build.BuildLifecycleController
import org.gradle.internal.build.BuildState
import org.gradle.internal.build.BuildToolingModelController
import org.gradle.internal.build.BuildToolingModelControllerFactory
import org.gradle.internal.build.DefaultBuildToolingModelController
import org.gradle.internal.buildtree.BuildModelParameters
import org.gradle.tooling.provider.model.internal.ToolingModelBuilderLookup


internal
class DefaultBuildToolingModelControllerFactory(
    private val modelParameters: BuildModelParameters
) : BuildToolingModelControllerFactory {
    override fun createController(owner: BuildState, controller: BuildLifecycleController): BuildToolingModelController {
        val defaultController = DefaultBuildToolingModelController(owner, controller, controller.gradle.services.get(ToolingModelBuilderLookup::class.java))
        return if (modelParameters.isIntermediateModelCache) {
            ConfigurationCacheAwareBuildToolingModelController(defaultController, controller.gradle.services.get(BuildTreeConfigurationCache::class.java))
        } else {
            defaultController
        }
    }
}
