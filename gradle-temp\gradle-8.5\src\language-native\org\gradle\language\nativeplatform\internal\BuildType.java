/*
 * Copyright 2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.language.nativeplatform.internal;

import org.gradle.api.Named;

import java.util.Arrays;
import java.util.Collection;

public final class BuildType implements Named {
    public static final BuildType DEBUG = new BuildType("debug", true, false);
    public static final BuildType RELEASE = new BuildType("release", true, true);
    public static final Collection<BuildType> DEFAULT_BUILD_TYPES = Arrays.asList(DEBUG, RELEASE);

    private final boolean debuggable;
    private final boolean optimized;
    private final String name;

    private BuildType(String name, boolean debuggable, boolean optimized) {
        this.debuggable = debuggable;
        this.optimized = optimized;
        this.name = name;
    }

    @Override
    public String getName() {
        return name;
    }

    public boolean isDebuggable() {
        return debuggable;
    }

    public boolean isOptimized() {
        return optimized;
    }
}
