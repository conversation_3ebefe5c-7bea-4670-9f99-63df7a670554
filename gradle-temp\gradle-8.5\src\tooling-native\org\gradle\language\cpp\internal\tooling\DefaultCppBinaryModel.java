/*
 * Copyright 2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.language.cpp.internal.tooling;

import java.io.Serializable;

public class DefaultCppBinaryModel implements Serializable {
    private final String name;
    private final String variantName;
    private final String baseName;
    private final DefaultCompilationDetails compilationDetails;
    private final DefaultLinkageDetails linkageDetails;

    public DefaultCppBinaryModel(String name, String variantName, String baseName, DefaultCompilationDetails compilationDetails, DefaultLinkageDetails linkageDetails) {
        this.name = name;
        this.variantName = variantName;
        this.baseName = baseName;
        this.compilationDetails = compilationDetails;
        this.linkageDetails = linkageDetails;
    }

    public String getName() {
        return name;
    }

    public String getVariantName() {
        return variantName;
    }

    public String getBaseName() {
        return baseName;
    }

    public DefaultCompilationDetails getCompilationDetails() {
        return compilationDetails;
    }

    public DefaultLinkageDetails getLinkageDetails() {
        return linkageDetails;
    }
}
