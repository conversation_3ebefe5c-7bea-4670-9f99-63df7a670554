/*
 * Copyright 2016 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.plugins.ide.internal.tooling.eclipse;

import java.io.Serializable;
import java.util.List;

public abstract class DefaultEclipseClasspathEntry implements Serializable {

    private final List<DefaultClasspathAttribute> classpathAttributes;
    private final List<DefaultAccessRule> accessRules;

    public DefaultEclipseClasspathEntry(List<DefaultClasspathAttribute> classpathAttributes, List<DefaultAccessRule> accessRules) {
        this.classpathAttributes = classpathAttributes;
        this.accessRules = accessRules;
    }

    public List<DefaultClasspathAttribute> getClasspathAttributes() {
        return classpathAttributes;
    }

    public List<DefaultAccessRule> getAccessRules() {
        return accessRules;
    }
}
