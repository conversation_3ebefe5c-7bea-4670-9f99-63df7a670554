/*
 * This Groovy source file was generated by the Gradle 'init' task.
 */
${packageDecl.statement}
import org.gradle.testfixtures.ProjectBuilder
import org.gradle.api.Project
import spock.lang.Specification

/**
 * A simple unit test for the '${pluginId.value}' plugin.
 */
class ${className.javaIdentifier} extends Specification {
    def "plugin registers task"() {
        given:
        def project = ProjectBuilder.builder().build()

        when:
        project.plugins.apply("${pluginId.value}")

        then:
        project.tasks.findByName("greeting") != null
    }
}
