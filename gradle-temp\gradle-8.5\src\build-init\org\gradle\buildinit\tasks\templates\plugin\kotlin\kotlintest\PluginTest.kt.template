/*
 * This Kotlin source file was generated by the Gradle 'init' task.
 */
${packageDecl.statement}
import org.gradle.testfixtures.ProjectBuilder
import kotlin.test.Test
import kotlin.test.assertNotNull

/**
 * A simple unit test for the '${pluginId.value}' plugin.
 */
class ${className.javaIdentifier} {
    @Test fun `plugin registers task`() {
        // Create a test project and apply the plugin
        val project = ProjectBuilder.builder().build()
        project.plugins.apply("${pluginId.value}")

        // Verify the result
        assertNotNull(project.tasks.findByName("greeting"))
    }
}
