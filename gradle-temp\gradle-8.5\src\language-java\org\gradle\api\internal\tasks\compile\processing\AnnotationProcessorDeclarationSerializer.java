/*
 * Copyright 2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.api.internal.tasks.compile.processing;

import org.gradle.api.internal.tasks.compile.incremental.processing.IncrementalAnnotationProcessorType;
import org.gradle.internal.serialize.Decoder;
import org.gradle.internal.serialize.Encoder;

import java.io.EOFException;

class AnnotationProcessorDeclarationSerializer implements org.gradle.internal.serialize.Serializer<AnnotationProcessorDeclaration> {
    public static final AnnotationProcessorDeclarationSerializer INSTANCE = new AnnotationProcessorDeclarationSerializer();

    private AnnotationProcessorDeclarationSerializer() {
    }

    @Override
    public AnnotationProcessorDeclaration read(Decoder decoder) throws EOFException, Exception {
        String name = decoder.readString();
        IncrementalAnnotationProcessorType type = IncrementalAnnotationProcessorType.values()[decoder.readSmallInt()];
        return new AnnotationProcessorDeclaration(name, type);
    }

    @Override
    public void write(Encoder encoder, AnnotationProcessorDeclaration value) throws Exception {
        encoder.writeString(value.getClassName());
        encoder.writeSmallInt(value.getType().ordinal());
    }
}
