/*
 * Copyright 2017 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.language.cpp.internal;

import org.gradle.language.cpp.CppPlatform;
import org.gradle.nativeplatform.TargetMachine;
import org.gradle.nativeplatform.platform.NativePlatform;

public class DefaultCppPlatform implements CppPlatform {
    private final TargetMachine targetMachine;
    private final NativePlatform nativePlatform;

    public DefaultCppPlatform(TargetMachine targetMachine) {
        this(targetMachine, null);
    }

    public DefaultCppPlatform(TargetMachine targetMachine, NativePlatform nativePlatform) {
        this.targetMachine = targetMachine;
        this.nativePlatform = nativePlatform;
    }

    @Override
    public TargetMachine getTargetMachine() {
        return targetMachine;
    }

    public NativePlatform getNativePlatform() {
        return nativePlatform;
    }
}
