/*
 * Copyright 2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.gradle.plugins.ide.eclipse.model;

import java.io.File;

/**
 * A reference to a file in eclipse.
 */
public interface FileReference {
    /**
     * Returns the logical path for the file.
     */
    String getPath();

    /**
     * Returns the target file.
     */
    File getFile();

    /**
     * Returns the jar URL of the file
     */
    String getJarURL();
    /**
     * Returns true if this reference is relative to a path variable.
     */
    boolean isRelativeToPathVariable();
}
