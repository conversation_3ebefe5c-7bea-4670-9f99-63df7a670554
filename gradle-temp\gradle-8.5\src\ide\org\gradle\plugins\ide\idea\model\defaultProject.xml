<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
    <component name="CompilerConfiguration">
        <option name="DEFAULT_COMPILER" value="Javac"/>
        <resourceExtensions>
            <entry name=".+\.(properties|xml|html|dtd|tld)"/>
            <entry name=".+\.(gif|png|jpeg|jpg)"/>
        </resourceExtensions>
        <wildcardResourcePatterns>
        </wildcardResourcePatterns>
        <annotationProcessing enabled="false" useClasspath="true"/>
    </component>
    <component name="CopyrightManager" default="">
        <module2copyright/>
    </component>
    <component name="DependencyValidationManager">
        <option name="SKIP_IMPORT_STATEMENTS" value="false"/>
    </component>
    <component name="Encoding" useUTFGuessing="true" native2AsciiForPropertiesFiles="false"/>
    <component name="GradleUISettings">
        <setting name="root"/>
    </component>
    <component name="GradleUISettings2">
        <setting name="root"/>
    </component>
    <component name="IdProvider" IDEtalkID="11DA1DB66DD62DDA1ED602B7079FE97C"/>
    <component name="JavadocGenerationManager">
        <option name="OUTPUT_DIRECTORY"/>
        <option name="OPTION_SCOPE" value="protected"/>
        <option name="OPTION_HIERARCHY" value="true"/>
        <option name="OPTION_NAVIGATOR" value="true"/>
        <option name="OPTION_INDEX" value="true"/>
        <option name="OPTION_SEPARATE_INDEX" value="true"/>
        <option name="OPTION_DOCUMENT_TAG_USE" value="false"/>
        <option name="OPTION_DOCUMENT_TAG_AUTHOR" value="false"/>
        <option name="OPTION_DOCUMENT_TAG_VERSION" value="false"/>
        <option name="OPTION_DOCUMENT_TAG_DEPRECATED" value="true"/>
        <option name="OPTION_DEPRECATED_LIST" value="true"/>
        <option name="OTHER_OPTIONS" value=""/>
        <option name="HEAP_SIZE"/>
        <option name="LOCALE"/>
        <option name="OPEN_IN_BROWSER" value="true"/>
    </component>
    <component name="ProjectModuleManager">
    </component>
    <component name="ProjectRootManager" version="2" languageLevel="JDK_1_5" assert-keyword="true" jdk-15="true"
               project-jdk-type="JavaSDK">
        <output url="file://$PROJECT_DIR$/out"/>
    </component>
    <component name="SvnBranchConfigurationManager">
        <option name="mySupportsUserInfoFilter" value="true"/>
    </component>
    <component name="VcsDirectoryMappings">
        <mapping directory="" vcs=""/>
    </component>
    <component name="masterDetails">
        <states>
            <state key="ArtifactsStructureConfigurable.UI">
                <UIState>
                    <splitter-proportions>
                        <SplitterProportionsDataImpl/>
                    </splitter-proportions>
                    <settings/>
                </UIState>
            </state>
            <state key="Copyright.UI">
                <UIState>
                    <splitter-proportions>
                        <SplitterProportionsDataImpl/>
                    </splitter-proportions>
                </UIState>
            </state>
            <state key="ProjectJDKs.UI">
                <UIState>
                    <splitter-proportions>
                        <SplitterProportionsDataImpl>
                            <option name="proportions">
                                <list>
                                    <option value="0.2"/>
                                </list>
                            </option>
                        </SplitterProportionsDataImpl>
                    </splitter-proportions>
                    <last-edited>1.6</last-edited>
                </UIState>
            </state>
            <state key="ScopeChooserConfigurable.UI">
                <UIState>
                    <splitter-proportions>
                        <SplitterProportionsDataImpl/>
                    </splitter-proportions>
                    <settings/>
                </UIState>
            </state>
        </states>
    </component>
</project>
