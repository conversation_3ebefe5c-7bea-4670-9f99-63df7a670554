<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager" flattened_view="true" show_ignored="false" />
  <component name="CreatePatchCommitExecutor">
    <option name="PATCH_PATH" value="" />
    <option name="REVERSE_PATCH" value="false" />
  </component>
  <component name="DaemonCodeAnalyzer">
    <disable_hints />
  </component>
  <component name="DebuggerManager">
    <breakpoint_any>
      <breakpoint>
        <option name="NOTIFY_CAUGHT" value="true" />
        <option name="NOTIFY_UNCAUGHT" value="true" />
        <option name="ENABLED" value="false" />
        <option name="LOG_ENABLED" value="false" />
        <option name="LOG_EXPRESSION_ENABLED" value="false" />
        <option name="SUSPEND_POLICY" value="SuspendAll" />
        <option name="COUNT_FILTER_ENABLED" value="false" />
        <option name="COUNT_FILTER" value="0" />
        <option name="CONDITION_ENABLED" value="false" />
        <option name="CLASS_FILTERS_ENABLED" value="false" />
        <option name="INSTANCE_FILTERS_ENABLED" value="false" />
        <option name="CONDITION" value="" />
        <option name="LOG_MESSAGE" value="" />
      </breakpoint>
      <breakpoint>
        <option name="NOTIFY_CAUGHT" value="true" />
        <option name="NOTIFY_UNCAUGHT" value="true" />
        <option name="ENABLED" value="false" />
        <option name="LOG_ENABLED" value="false" />
        <option name="LOG_EXPRESSION_ENABLED" value="false" />
        <option name="SUSPEND_POLICY" value="SuspendAll" />
        <option name="COUNT_FILTER_ENABLED" value="false" />
        <option name="COUNT_FILTER" value="0" />
        <option name="CONDITION_ENABLED" value="false" />
        <option name="CLASS_FILTERS_ENABLED" value="false" />
        <option name="INSTANCE_FILTERS_ENABLED" value="false" />
        <option name="CONDITION" value="" />
        <option name="LOG_MESSAGE" value="" />
      </breakpoint>
    </breakpoint_any>
    <breakpoint_rules />
    <ui_properties />
  </component>
  <component name="ModuleEditorState">
    <option name="LAST_EDITED_MODULE_NAME" />
    <option name="LAST_EDITED_TAB_NAME" />
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state />
    </entry>
  </component>
  <component name="ProjectLevelVcsManager">
    <OptionsSetting value="true" id="Add" />
    <OptionsSetting value="true" id="Remove" />
    <OptionsSetting value="true" id="Checkout" />
    <OptionsSetting value="true" id="Update" />
    <OptionsSetting value="true" id="Status" />
    <OptionsSetting value="true" id="Edit" />
    <ConfirmationsSetting value="0" id="Add" />
    <ConfirmationsSetting value="0" id="Remove" />
  </component>
  <component name="ProjectReloadState">
    <option name="STATE" value="0" />
  </component>
  <component name="PropertiesComponent">
    <property name="GoToFile.includeJavaFiles" value="false" />
    <property name="GoToClass.toSaveIncludeLibraries" value="false" />
    <property name="MemberChooser.sorted" value="false" />
    <property name="MemberChooser.showClasses" value="true" />
    <property name="GoToClass.includeLibraries" value="false" />
    <property name="MemberChooser.copyJavadoc" value="false" />
  </component>
  <component name="RunManager">
    <configuration default="true" type="Remote" factoryName="Remote">
      <option name="USE_SOCKET_TRANSPORT" value="true" />
      <option name="SERVER_MODE" value="false" />
      <option name="SHMEM_ADDRESS" value="javadebug" />
      <option name="HOST" value="localhost" />
      <option name="PORT" value="5005" />
      <method>
        <option name="BuildArtifacts" enabled="false" />
      </method>
    </configuration>
    <configuration default="true" type="Applet" factoryName="Applet">
      <module name="" />
      <option name="MAIN_CLASS_NAME" />
      <option name="HTML_FILE_NAME" />
      <option name="HTML_USED" value="false" />
      <option name="WIDTH" value="400" />
      <option name="HEIGHT" value="300" />
      <option name="POLICY_FILE" value="$APPLICATION_HOME_DIR$/bin/appletviewer.policy" />
      <option name="VM_PARAMETERS" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <method>
        <option name="BuildArtifacts" enabled="false" />
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <extension name="coverage" enabled="false" merge="false" />
      <option name="MAIN_CLASS_NAME" />
      <option name="VM_PARAMETERS" />
      <option name="PROGRAM_PARAMETERS" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="ENABLE_SWING_INSPECTOR" value="false" />
      <option name="ENV_VARIABLES" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <module name="" />
      <envs />
      <method>
        <option name="BuildArtifacts" enabled="false" />
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <extension name="coverage" enabled="false" merge="false" />
      <module name="" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="PACKAGE_NAME" />
      <option name="MAIN_CLASS_NAME" />
      <option name="METHOD_NAME" />
      <option name="TEST_OBJECT" value="class" />
      <option name="VM_PARAMETERS" />
      <option name="PARAMETERS" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="ENV_VARIABLES" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <option name="TEST_SEARCH_SCOPE">
        <value defaultName="moduleWithDependencies" />
      </option>
      <envs />
      <method>
        <option name="BuildArtifacts" enabled="false" />
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list size="0" />
    <configuration name="&lt;template&gt;" type="WebApp" default="true" selected="false">
      <Host>localhost</Host>
      <Port>5050</Port>
    </configuration>
  </component>
  <component name="ShelveChangesManager" show_recycled="false" />
  <component name="SvnConfiguration" maxAnnotateRevisions="500">
    <option name="USER" value="" />
    <option name="PASSWORD" value="" />
    <option name="LAST_MERGED_REVISION" />
    <option name="UPDATE_RUN_STATUS" value="false" />
    <option name="MERGE_DRY_RUN" value="false" />
    <option name="MERGE_DIFF_USE_ANCESTRY" value="true" />
    <option name="UPDATE_LOCK_ON_DEMAND" value="false" />
    <option name="IGNORE_SPACES_IN_MERGE" value="false" />
    <option name="DETECT_NESTED_COPIES" value="true" />
    <option name="IGNORE_SPACES_IN_ANNOTATE" value="true" />
    <option name="SHOW_MERGE_SOURCES_IN_ANNOTATE" value="true" />
    <myIsUseDefaultProxy>false</myIsUseDefaultProxy>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="OFFER_MOVE_TO_ANOTHER_CHANGELIST_ON_PARTIAL_COMMIT" value="true" />
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="true" />
    <option name="PERFORM_UPDATE_IN_BACKGROUND" value="true" />
    <option name="PERFORM_COMMIT_IN_BACKGROUND" value="true" />
    <option name="PERFORM_EDIT_IN_BACKGROUND" value="true" />
    <option name="PERFORM_CHECKOUT_IN_BACKGROUND" value="true" />
    <option name="PERFORM_ADD_REMOVE_IN_BACKGROUND" value="true" />
    <option name="PERFORM_ROLLBACK_IN_BACKGROUND" value="false" />
    <option name="CHECK_LOCALLY_CHANGED_CONFLICTS_IN_BACKGROUND" value="false" />
    <option name="ENABLE_BACKGROUND_PROCESSES" value="false" />
    <option name="CHANGED_ON_SERVER_INTERVAL" value="60" />
    <option name="FORCE_NON_EMPTY_COMMENT" value="false" />
    <option name="LAST_COMMIT_MESSAGE" />
    <option name="MAKE_NEW_CHANGELIST_ACTIVE" value="true" />
    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_FILES_UP_TO_DATE_BEFORE_COMMIT" value="false" />
    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="REFORMAT_BEFORE_FILE_COMMIT" value="false" />
    <option name="FILE_HISTORY_DIALOG_COMMENTS_SPLITTER_PROPORTION" value="0.8" />
    <option name="FILE_HISTORY_DIALOG_SPLITTER_PROPORTION" value="0.5" />
    <option name="ACTIVE_VCS_NAME" />
    <option name="UPDATE_GROUP_BY_PACKAGES" value="false" />
    <option name="UPDATE_GROUP_BY_CHANGELIST" value="false" />
    <option name="SHOW_FILE_HISTORY_AS_TREE" value="false" />
    <option name="FILE_HISTORY_SPLITTER_PROPORTION" value="0.6" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager />
  </component>
</project>

