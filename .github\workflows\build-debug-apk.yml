name: Build Debug APK (Quick Test)

on:
  workflow_dispatch:  # Manual trigger only
  
jobs:
  build-debug-apk:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'
        
    - name: Setup Android SDK
      uses: android-actions/setup-android@v3
      
    - name: Install dependencies
      run: npm ci
      
    - name: Build web app
      run: npm run build
      
    - name: Sync Capacitor
      run: npx cap sync android
      
    - name: Build Debug APK
      run: |
        cd android
        chmod +x gradlew
        ./gradlew assembleDebug
        
    - name: Upload Debug APK
      uses: actions/upload-artifact@v4
      with:
        name: BudgetTrackerPro-Debug-APK
        path: android/app/build/outputs/apk/debug/app-debug.apk
        retention-days: 7
