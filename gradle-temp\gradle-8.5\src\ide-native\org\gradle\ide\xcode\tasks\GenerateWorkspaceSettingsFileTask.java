/*
 * Copyright 2017 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.ide.xcode.tasks;

import org.gradle.api.Incubating;
import org.gradle.ide.xcode.tasks.internal.XcodeWorkspaceSettingsFile;
import org.gradle.internal.Cast;
import org.gradle.plugins.ide.api.PropertyListGeneratorTask;
import org.gradle.work.DisableCachingByDefault;

/**
 * Task for generating a Xcode workspace settings file (e.g. {@code Foo.xcodeproj/project.xcworkspace/xcshareddata/WorkspaceSettings.xcsettings}).
 *
 * <p>This task is used in conjunction with {@link org.gradle.ide.xcode.tasks.GenerateXcodeWorkspaceFileTask}.</p>
 *
 * @see org.gradle.ide.xcode.XcodeWorkspace
 * @since 4.2
 */
@Incubating
@DisableCachingByDefault(because = "Not made cacheable, yet")
public abstract class GenerateWorkspaceSettingsFileTask extends PropertyListGeneratorTask<XcodeWorkspaceSettingsFile> {
    @Override
    protected void configure(XcodeWorkspaceSettingsFile settingsFile) {
    }

    @Override
    protected XcodeWorkspaceSettingsFile create() {
        return new XcodeWorkspaceSettingsFile(Cast.uncheckedNonnullCast(getPropertyListTransformer()));
    }
}
