/*
 * This Groovy source file was generated by the Gradle 'init' task.
 */
${packageDecl.statement}
import ${basePackageName.raw}.list.LinkedList

import static ${basePackageName.raw}.utilities.StringUtils.join
import static ${basePackageName.raw}.utilities.StringUtils.split
import static ${basePackageName.raw}.app.MessageUtils.getMessage

import org.apache.commons.text.WordUtils

class App {
    static void main(String[] args) {
        LinkedList tokens
        tokens = split(getMessage())
        String result = join(tokens)
        println(WordUtils.capitalize(result))
    }
}
