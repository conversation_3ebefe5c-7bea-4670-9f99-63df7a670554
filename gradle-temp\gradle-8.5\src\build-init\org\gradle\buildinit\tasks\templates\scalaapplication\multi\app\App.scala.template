/*
 * This Scala source file was generated by the Gradle 'init' task.
 */
${packageDecl.statement}
import ${basePackageName.raw}.utilities.StringUtils

import org.apache.commons.text.WordUtils

object App {
   def main(args: Array[String]): Unit = {
        val tokens = StringUtils.split(MessageUtils.getMessage())
        val result = StringUtils.join(tokens)
        println(WordUtils.capitalize(result))
    }
}
