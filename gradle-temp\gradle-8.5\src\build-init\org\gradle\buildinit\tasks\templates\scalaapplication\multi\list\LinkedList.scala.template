/*
 * This Scala source file was generated by the Gradle 'init' task.
 */
${packageDecl.statement}

class LinkedList {
    private var head: Node = _

    def add(element: String): Unit = {
        val newNode = new Node(element)

        val it = tail(head)
        if (it == null) {
            head = newNode
        } else {
            it.next = newNode
        }
    }

    private def tail(head: Node) = {
        var it: Node = null

        it = head
        while (it != null && it.next != null) {
            it = it.next
        }

        it
    }

    def remove(element: String): Boolean = {
        var result = false
        var previousIt: Node = null
        var it: Node = head
        while (!result && it != null) {
            if (0 == element.compareTo(it.data)) {
                result = true
                unlink(previousIt, it)
            }
            previousIt = it
            it = it.next
        }

        result
    }

    private def unlink(previousIt: Node, currentIt: Node): Unit = {
        if (currentIt == head) {
            head = currentIt.next
        } else {
            previousIt.next = currentIt.next
        }
    }

    def size(): Int = {
        var size = 0

        var it = head
        while (it != null) {
            size = size + 1
            it = it.next
        }

        size
    }

    def get(idx: Int): String = {
        var index = idx
        var it = head
        while (index > 0 && it != null) {
            it = it.next
            index = index - 1
        }

        if (it == null) {
            throw new IndexOutOfBoundsException("Index is out of range")
        }

        it.data
    }

    private class Node(val data: String) {
        var next: Node = _
    }
}
