/*
 * Copyright 2017 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.language.nativeplatform.internal.incremental.sourceparser;

import org.gradle.cache.internal.FileContentCache;
import org.gradle.cache.internal.FileContentCacheFactory;
import org.gradle.language.nativeplatform.internal.IncludeDirectives;

import java.io.File;

public class CachingCSourceParser implements CSourceParser {
    private final FileContentCache<IncludeDirectives> cache;

    public CachingCSourceParser(FileContentCacheFactory cacheFactory) {
        final RegexBackedCSourceParser parser = new RegexBackedCSourceParser();
        cache = cacheFactory.newCache("parsedCSource", 40000, new FileContentCacheFactory.Calculator<IncludeDirectives>() {
            @Override
            public IncludeDirectives calculate(File file, boolean isRegularFile) {
                return parser.parseSource(file);
            }
        }, IncludeDirectivesSerializer.INSTANCE);
    }

    @Override
    public IncludeDirectives parseSource(File sourceFile) {
        return cache.get(sourceFile);
    }
}
