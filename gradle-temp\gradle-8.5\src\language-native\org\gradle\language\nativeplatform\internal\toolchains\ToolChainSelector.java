/*
 * Copyright 2017 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.language.nativeplatform.internal.toolchains;

import org.gradle.nativeplatform.toolchain.internal.NativeToolChainInternal;
import org.gradle.nativeplatform.toolchain.internal.PlatformToolProvider;

public interface ToolChainSelector {
    /**
     * Selects a platform of the given type that can run on the host machine, and a toolchain to build for this platform.
     */
    <T> Result<T> select(Class<T> platformType, T requestedPlatform);

    interface Result<T> {
        NativeToolChainInternal getToolChain();

        T getTargetPlatform();

        PlatformToolProvider getPlatformToolProvider();
    }
}
