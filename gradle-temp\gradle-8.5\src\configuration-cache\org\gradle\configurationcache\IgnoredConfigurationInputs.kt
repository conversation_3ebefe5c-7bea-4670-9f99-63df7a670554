/*
 * Copyright 2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.configurationcache

import java.io.File


/**
 * Defines specific configuration inputs that should not be included in the configuration cache fingerprint.
 */
interface IgnoredConfigurationInputs {
    /**
     * Checks if the [file] should be excluded from the configuration inputs fingerprint if it participates
     * in file system checks, such as [File.exists], [File.isFile] etc.
     */
    fun isFileSystemCheckIgnoredFor(file: File): Boolean
}
