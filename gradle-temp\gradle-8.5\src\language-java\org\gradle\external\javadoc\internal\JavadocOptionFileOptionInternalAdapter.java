/*
 * Copyright 2017 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.external.javadoc.internal;

import org.gradle.external.javadoc.JavadocOptionFileOption;

import java.io.IOException;

public class JavadocOptionFileOptionInternalAdapter<T> implements JavadocOptionFileOptionInternal<T> {
    private final JavadocOptionFileOption<T> option;

    public JavadocOptionFileOptionInternalAdapter(JavadocOptionFileOption<T> option) {
        this.option = option;
    }

    @Override
    public JavadocOptionFileOptionInternal<T> duplicate() {
        // TODO: We basically don't support copying custom Javadoc options
        return this;
    }

    @Override
    public String getOption() {
        return option.getOption();
    }

    @Override
    public T getValue() {
        return option.getValue();
    }

    @Override
    public void setValue(T value) {
        option.setValue(value);
    }

    @Override
    public void write(JavadocOptionFileWriterContext writerContext) throws IOException {
        option.write(writerContext);
    }
}
