/*
 * Copyright 2017 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.ide.xcode.tasks.internal;

import com.dd.plist.NSDictionary;
import org.gradle.api.internal.PropertyListTransformer;
import org.gradle.plugins.ide.internal.generator.PropertyListPersistableConfigurationObject;

public class XcodeProjectFile extends PropertyListPersistableConfigurationObject<NSDictionary> {

    public XcodeProjectFile(PropertyListTransformer<NSDictionary> transformer) {
        super(NSDictionary.class, transformer);
    }

    @Override
    protected NSDictionary newRootObject() {
        return new NSDictionary();
    }

    @Override
    protected void store(NSDictionary rootObject) {

    }

    @Override
    protected void load(NSDictionary rootObject) {

    }

    @Override
    protected String getDefaultResourceName() {
        return "default.pbxproj";
    }
}
