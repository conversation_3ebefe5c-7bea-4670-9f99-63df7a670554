/*
 * This Java source file was generated by the Gradle 'init' task.
 */
${packageDecl.javaStatement}
import ${basePackageName.raw}.list.LinkedList;

import static ${basePackageName.raw}.utilities.StringUtils.join;
import static ${basePackageName.raw}.utilities.StringUtils.split;
import static ${basePackageName.raw}.app.MessageUtils.getMessage;

import org.apache.commons.text.WordUtils;

public class App {
    public static void main(String[] args) {
        LinkedList tokens;
        tokens = split(getMessage());
        String result = join(tokens);
        System.out.println(WordUtils.capitalize(result));
    }
}
