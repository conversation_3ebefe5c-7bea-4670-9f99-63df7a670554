/*
 * This C++ source file was generated by the Gradle 'init' task.
 */
#ifndef ${namespace.groovyString.toUpperCase()}_H
#define ${namespace.groovyString.toUpperCase()}_H

#ifdef _WIN32
#define ${namespace.groovyString.toUpperCase()}_EXPORT_FUNC __declspec(dllexport)
#else
#define ${namespace.groovyString.toUpperCase()}_EXPORT_FUNC
#endif

#include <string>

namespace ${namespace.groovyString} {
    class Greeter {
        public:
        std::string ${namespace.groovyString.toUpperCase()}_EXPORT_FUNC greeting();
    };
}

#endif
