/*
 * Copyright 2016 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.tooling.internal.provider.serialization;

import org.gradle.internal.classloader.ClassLoaderSpec;

import java.net.URL;
import java.util.List;

public class ClientOwnedClassLoaderSpec extends ClassLoaderSpec {
    private final List<URL> classpath;

    public ClientOwnedClassLoaderSpec(List<URL> classpath) {
        this.classpath = classpath;
    }

    public List<URL> getClasspath() {
        return classpath;
    }

    @Override
    public String toString() {
        return "{client-owned-class-loader classpath: " + classpath + "}";
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj == null || obj.getClass() != getClass()) {
            return false;
        }
        ClientOwnedClassLoaderSpec other = (ClientOwnedClassLoaderSpec) obj;
        return classpath.equals(other.classpath);
    }

    @Override
    public int hashCode() {
        return classpath.hashCode();
    }
}
