/*
 * Copyright 2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.ide.xcode;

import org.gradle.api.file.Directory;
import org.gradle.api.provider.Provider;
import org.gradle.plugins.ide.IdeWorkspace;

/**
 * Represents the generated Xcode workspace.
 *
 * @since 4.7
 */
public interface XcodeWorkspace extends IdeWorkspace {
    /**
     * Returns the location of the generated workspace.
     */
    @Override
    Provider<Directory> getLocation();
}
