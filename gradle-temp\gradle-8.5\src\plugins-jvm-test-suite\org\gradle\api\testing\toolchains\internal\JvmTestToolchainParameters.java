/*
 * Copyright 2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.api.testing.toolchains.internal;

/**
 * Marker interface for parameters of {@link JvmTestToolchain}s.  Implementations of {@link JvmTestToolchain} be parameterized with
 * an implementation of this type or {@link JvmTestToolchainParameters.None}.
 *
 * @since 8.5
 */
public interface JvmTestToolchainParameters {
    /**
     * Marker interface for {@link JvmTestToolchain} implementations that do not require any configuration.
     */
    final class None implements JvmTestToolchainParameters {}
}
