/*
 * Copyright 2013 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.testing.jacoco.tasks;

import org.gradle.api.reporting.ConfigurableReport;
import org.gradle.api.reporting.DirectoryReport;
import org.gradle.api.reporting.ReportContainer;
import org.gradle.api.reporting.SingleFileReport;
import org.gradle.api.tasks.Internal;

/**
 * The reporting configuration for the {@link JacocoReport} task.
 */
public interface JacocoReportsContainer extends ReportContainer<ConfigurableReport> {
    /**
     * The JaCoCo HTML report
     *
     * @return The JaCoCo HTML report
     */
    @Internal
    DirectoryReport getHtml();

    /**
     * The JaCoCo (single file) XML report
     *
     * @return The JaCoCo (single file) XML report
     */
    @Internal
    SingleFileReport getXml();

    /**
     * The JaCoCo (single file) CSV report
     *
     * @return The JaCoCo (single file) CSV report
     */
    @Internal
    SingleFileReport getCsv();
}
