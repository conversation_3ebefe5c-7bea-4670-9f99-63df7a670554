/*
 * Copyright 2016 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.kotlin.dsl

import org.gradle.api.file.CopySpec

import java.io.FilterReader


/**
 * Adds a content filter to be used during the copy.
 *
 * @see [CopySpec.filter]
 */
inline fun <reified T : FilterReader> CopySpec.filter(vararg properties: Pair<String, Any?>) =
    filter(mapOf(*properties), T::class.java)


/**
 * Adds a content filter to be used during the copy.
 *
 * @see [CopySpec.filter]
 */
inline fun <reified T : FilterReader> CopySpec.filter(properties: Map<String, Any?>) =
    filter(properties, T::class.java)
